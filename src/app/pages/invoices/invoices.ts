import { Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { DataService } from "src/app/services/data";
import { CommonService } from "src/app/services/common";
import { TypeSenseService } from "src/app/services/typesense";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    errorMessage = '';
    ordersData: any[] = [];

    constructor(
        private data: DataService,
        private commonService: CommonService,
        private typesenseService: TypeSenseService
    ) {
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        this.loadOrders();
    }
    loadOrders() {
        this.isLoading = true;
        this.errorMessage = '';

        this.commonService.get('get_all_orders', { limit: 100 }).subscribe({
            next: (response) => {
                console.log('Orders fetched successfully:', response);

                let ordersArray: any[] = [];

                if (Array.isArray(response)) {
                    ordersArray = response;
                } else  {
                    console.log('Unknown response structure:', response);
                }

                this.invoices = ordersArray.map((order: any) => {
                    const items = Array.isArray(order.items) ? order.items : [];

                    return {
                        ...order,
                        items: items.map((item: any) => ({
                            ...item,
                            sku : item.sku,
                            price: item.sale_price || item.price || 0,
                            quantity: item.quantity || 1
                        }))
                    };
                });

                console.log('Final mapped orders:', this.invoices);
                this.isLoading = false;
            },
            error: (error) => {
                console.error('Error fetching orders:', error);
                this.errorMessage = 'Failed to load orders. Please try again.';
                this.isLoading = false;
            }
        });
    }
    async onSelect(event: any){
        //real API data 
        this.ordersData = event?.event?.data || [];
        const rawItems = Array.isArray(this.ordersData) ? this.ordersData[0]?.items || [] : (this.ordersData as any).items || [];

        if (rawItems.length > 0) {
            //  items with complete product details from TypeSense
            const detailedItems = await Promise.all(
                rawItems.map(async (item: any) => {
                    console.log('Processing invoice item:', item);
                    console.log('Item SKU:', item.sku);

                    if (!item.sku) {
                        console.warn('Invoice item missing SKU, using item data directly:', item);
                        return {
                            ...item,
                            name: item.name || 'Unknown Product',
                            image: item.image || item.thumbnail_url || '',
                            thumbnail_url: item.thumbnail_url || item.image || ''
                        };
                    }

                    const productDetails = await this.typesenseService.getProductBySku(item.sku);

                    if (productDetails) {
                        console.log('Found product details for invoice item SKU:', item.sku, productDetails);
                        // Merge backend item data with complete product details from TypeSense
                        return {
                            ...item, 
                            id: item.id || item.sku,
                            name: productDetails.name || item.name || 'Unknown Product',
                            description: productDetails.description || '',
                            image: productDetails.image_url?.[0] || productDetails.thumbnail_url || '',
                            thumbnail_url: productDetails.thumbnail_url || productDetails.image_url?.[0] || '',
                            image_url: productDetails.image_url || [],
                        };
                    } else {
                        console.warn(`Product with SKU ${item.sku} not found in TypeSense`);
                    }
                })
            );

            this.cartItems = detailedItems;
        } else {
            this.cartItems = [];
        }    
    }

    downloadInvoice() {
        if (!this.ordersData || this.ordersData.length === 0) {
            this.commonService.toast({
                severity: 'warn',
                summary: 'Warning',
                detail: 'Please select an order to download invoice'
            });
            return;
        }

        const order = Array.isArray(this.ordersData) ? this.ordersData[0] : this.ordersData;
        
        // Format order details for PDF
        const invoiceData = {
            orderNumber: order.order_id,
            customerName: order.customer_name,
            date: new Date(order.created_at).toLocaleDateString(),
            items: order.items.map((item: any) => ({
                name: item.name || 'Unknown Product',
                quantity: item.quantity,
                price: item.price,
                total: item.quantity * item.price
            })),
            subtotal: order.total_amount,
            discount: order.total_amount * 0.1,
            total: order.total_amount * 0.9
        };
    }
}