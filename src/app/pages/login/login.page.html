<ion-content [fullscreen]="true">
    <div class="min-h-screen  flex items-center justify-center p-4">
        <div
            class="bg-white shadow-xl rounded-3xl overflow-hidden max-w-5xl w-full h-[500px] flex flex-col lg:flex-row">
            <div
                class="hidden lg:flex lg:w-1/2 bg-gradient-to-l from-blue-900 to-blue-600 flex-col justify-center items-center p-12 text-white relative">
                <div class="relative z-10 text-center">
                    <div class="flex justify-center w-full mb-8">
                        <div class="bg-white rounded-lg p-4 shadow-lg">
                            <img class="w-32 h-auto"
                                src="https://djpw4cfh60y52.cloudfront.net/uploads/logo/ljD8TdmW3xUCdu1eHw8Gd31UIsMrYpt6DVHm3kRy.jpg"
                                alt="Logo" />
                        </div>
                    </div>
                    <h1 class="text-4xl font-bold mb-4 leading-tight">Welcome Back!</h1>
                    <p class="text-xl text-blue-100 leading-relaxed">Sign in to access your POS dashboard</p>

                </div>
            </div>

            <div class="w-full lg:w-1/2 p-8 lg:p-12 flex items-center justify-center">
                <div class="w-full max-w-md">
                    <div class="lg:hidden flex justify-center mb-8">
                        <img class="w-24 h-auto"
                            src="https://djpw4cfh60y52.cloudfront.net/uploads/logo/ljD8TdmW3xUCdu1eHw8Gd31UIsMrYpt6DVHm3kRy.jpg"
                            alt="Logo" />
                    </div>

                    <div class="text-center mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">Login</h2>
                    </div>

                    <form class="space-y-6" (ngSubmit)="onLogin()">
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="username">Username</label>
                            <div class="relative flex items-center">
                                <input id="username" type="text" placeholder="Enter your username" pInputText
                                    [(ngModel)]="username" name="username"
                                    class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white" />

                            </div>
                        </div>

                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="password">Password</label>

                            <div class="relative">
                                <input id="password" type="password" placeholder="Enter your password" pInputText
                                    [(ngModel)]="password" name="password"
                                    class="w-full border border-gray-300 rounded-lg px-4 py-3 pl-11 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white" />

                            </div>
                        </div>

                        <div class="text-sm text-right">
                            <a href="#"
                                class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">Forgot
                                Password?</a>
                        </div>


                        <button type="submit" (click)="onLogin()" (keyup.enter)="onLogin()" label="Login"
                            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg px-4 py-3 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-200 focus:ring-offset-1 transform transition-all duration-500 hover:scale-105 shadow-lg">
                            <span class="flex items-center justify-center">
                                Login
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</ion-content>