import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonContent } from '@ionic/angular/standalone';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { CommonService } from 'src/app/services/common';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'app-login',
  standalone: true,
  templateUrl: './login.page.html',
  imports: [
    CommonModule,
    IonContent,
    InputTextModule,
    ButtonModule,
    FormsModule,
  ],
})
export class LoginPage {
  username: string = '';
  password: string = '';
  constructor(
    private commonService: CommonService,
    private router: Router,
  ) {}

  async onLogin() {
    if (!this.username || !this.password) {
      this.commonService.toast({
        severity: 'error',
        summary: 'Error',
        detail: 'Please enter username and password',
      });
      return;
    }
    try {
      const logindata = {
        username: this.username,
        password: this.password,
      };

      this.commonService.post('api/auth/login/', logindata).subscribe({
        next: (response: any) => {
          if (response?.access) {
            // localStorage.setItem('token', response.access);
            this.router.navigate(['/home']);
          }
        },
        error: () => {
          this.commonService.toast({
            severity: 'error',
            summary: 'Error',
            detail: 'Please enter username and password',
          });
        },
      });
    } catch (error) {
      this.commonService.toast({
        severity: 'error',
        summary: 'Error',
        detail: 'Invalid credentials',
      });
    }
  }
}
